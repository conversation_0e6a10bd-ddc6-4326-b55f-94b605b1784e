name: Renovate

on:
  schedule:
    - cron: "0 0 * * *"
  push:
    branches:
      - localApps
  workflow_dispatch:
    inputs:
      manual-trigger:
        description: 'Manually trigger Renovate'
        default: ''

jobs:
  renovate:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Run Renovate
        uses: renovatebot/github-action@v41.0.6
        with:
          useSlim: false
          token: ${{ secrets.GITHUBTOKEN }}
