name: Update app version in Renovate Branches

on:
  push:
    branches: [ 'renovate/*' ]
  workflow_dispatch:
    inputs:
      manual-trigger:
        description: 'Manually trigger Renovate'
        default: ''
jobs:
  update-app-version:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4
        with:
          fetch-depth: 0

      - name: Configure repo
        run: |
          git config --local user.email "<EMAIL>"
          git config --local user.name "github-action update-app-version"

      - name: Get list of updated files by the last commit in this branch separated by space
        id: updated-files
        run: |
          echo "::set-output name=files::$(git diff-tree --no-commit-id --name-only -r ${{ github.sha }} | tr '\n' ' ')"

      - name: Run renovate-app-version.sh on updated files
        run: |
          IFS=' ' read -ra files <<< "${{ steps.updated-files.outputs.files }}"

          for file in "${files[@]}"; do
            if [[ $file == *"docker-compose.yml"* ]]; then
              app_name=$(echo $file | cut -d'/' -f 2)
              old_version=$(echo $file | cut -d'/' -f 3)
              chmod +x .github/workflows/renovate-app-version.sh
              .github/workflows/renovate-app-version.sh $app_name $old_version
            fi
          done

      - name: Commit & Push Changes
        run: |
          IFS=' ' read -ra files <<< "${{ steps.updated-files.outputs.files }}"

          for file in "${files[@]}"; do
            if [[ $file == *"docker-compose.yml"* ]]; then
              app_name=$(echo $file | cut -d'/' -f 2)
              git add "apps/$app_name/*" && git commit -m "Update app version [skip ci]" --no-verify && git push || true
            fi
          done