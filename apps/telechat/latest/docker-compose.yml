services:
  telechat:
    image: yym68686/chatgpt:latest
    container_name: ${CONTAINER_NAME}
    restart: always
    networks:
      - 1panel-network
    ports:
      - "${PANEL_APP_PORT_HTTP}:${PANEL_APP_PORT_HTTP}"
      - "${BETA_PORT}:${BETA_PORT}"
    environment:
      - PORT=${PANEL_APP_PORT_HTTP}
      - BASE_URL=${BASE_URL}
      - BETA_PORT=${BETA_PORT}
      - BETA_BASE_URL=${BETA_BASE_URL}
    labels:
      createdBy: "Apps"

networks:
  1panel-network:
    external: true
